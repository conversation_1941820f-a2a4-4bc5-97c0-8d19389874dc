# Nginx 集成配置指南

## 配置概述

已为客户的生产环境 Nginx 添加了 Moodplay 项目的配置，保持了原有服务的完整性。

## 新增配置内容

### 1. Upstream 配置

```nginx
# Moodplay Backend API 服务
upstream moodplay_backend {
    server 127.0.0.1:3001;  # 对应 docker-compose 中的端口映射
    least_conn;
    keepalive 16;
}

# Moodplay Admin Frontend 服务
upstream moodplay_admin {
    server 127.0.0.1:5173;  # 对应 docker-compose 中的端口映射
    least_conn;
    keepalive 8;
}
```

### 2. 新增 Server 块

为 `moodplay.top` 域名添加了独立的 server 块，监听相同的 8090 端口。

## 访问路径

### 管理后台
- **URL**: `https://moodplay.top/admin/`
- **代理到**: `http://127.0.0.1:5173/admin/`
- **说明**: 访问 React 管理后台界面

### API 接口
- **URL**: `https://moodplay.top/api/`
- **代理到**: `http://127.0.0.1:3001/`
- **说明**: 访问 Node.js Backend API
- **示例**: `https://moodplay.top/api/users` → `http://127.0.0.1:3001/users`

### 根路径
- **URL**: `https://moodplay.top/`
- **行为**: 自动重定向到 `/admin/`

## 部署步骤

### 1. 备份现有配置
```bash
sudo cp /etc/nginx/conf.d/default.conf /etc/nginx/conf.d/default.conf.backup
```

### 2. 更新 Nginx 配置
```bash
# 将 nginx-production-final.conf 的内容替换到 /etc/nginx/conf.d/default.conf
sudo cp nginx-production-final.conf /etc/nginx/conf.d/default.conf
```

### 3. SSL 证书配置

**重要**: 需要为 `moodplay.top` 配置 SSL 证书

#### 选项 A: 使用现有证书（临时方案）
当前配置使用了 `codejoyai.com` 的证书，这只是临时方案。

#### 选项 B: 配置新证书（推荐）
```nginx
# 在 moodplay.top server 块中更新
ssl_certificate /opt/cert/moodplay.top.pem;
ssl_certificate_key /opt/cert/moodplay.top.key;
```

### 4. 测试配置
```bash
# 测试 Nginx 配置语法
sudo nginx -t

# 如果测试通过，重新加载配置
sudo nginx -s reload
```

### 5. 启动 Moodplay 服务
```bash
# 在 moodplay 项目目录
docker-compose -f docker-compose.prod.yml up -d
```

## 验证部署

### 检查服务状态
```bash
# 检查 Docker 容器
docker-compose -f docker-compose.prod.yml ps

# 检查端口监听
netstat -tlnp | grep -E "(3001|5173)"
```

### 测试访问
```bash
# 测试管理后台
curl -k https://moodplay.top/admin/

# 测试 API
curl -k https://moodplay.top/api/health
```

## 配置特点

### 1. 端口隔离
- Moodplay Backend: `3001` (避免与客户 FastAPI 的 8000 冲突)
- Moodplay Admin: `5173` (独立端口)
- Moodplay MySQL: `3306` (容器内部，外部映射已调整)
- Moodplay Redis: `6379` (容器内部，外部映射已调整)

### 2. 路径隔离
- 客户服务: `/` 和 `/api/` (在 wechat.codejoyai.com 域名下)
- Moodplay 管理后台: `/admin/` (在 moodplay.top 域名下)
- Moodplay API: `/api/` (在 moodplay.top 域名下)

### 3. 静态资源优化
- 前端静态资源设置了 1 年缓存
- 支持常见的静态文件类型

### 4. 安全配置
- 继承了客户的安全头配置
- 使用相同的 SSL 配置标准

## 故障排除

### 1. 502 Bad Gateway
- 检查 Docker 容器是否正常运行
- 检查端口映射是否正确
- 查看 Nginx 错误日志: `sudo tail -f /var/log/nginx/error.log`

### 2. 404 Not Found
- 检查路径配置是否正确
- 确认前端项目的 `base: '/admin/'` 配置

### 3. SSL 证书问题
- 确认证书文件路径正确
- 检查证书是否包含 `moodplay.top` 域名

## 监控建议

### 日志监控
```bash
# Nginx 访问日志
sudo tail -f /var/log/nginx/access.log | grep moodplay

# Nginx 错误日志
sudo tail -f /var/log/nginx/error.log

# Docker 容器日志
docker-compose -f docker-compose.prod.yml logs -f
```

### 性能监控
- 监控 upstream 服务的响应时间
- 关注静态资源的缓存命中率
- 监控 SSL 握手性能
