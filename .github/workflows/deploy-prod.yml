name: Deploy to Production Environment

on:
  push:
    branches: [ main ]  # 当推送到 main 分支时触发

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: SSH and Deploy
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.PROD_SERVER_HOST }}
          username: ${{ secrets.PROD_SERVER_USER }}
          key: ${{ secrets.PROD_SERVER_SSH_KEY }}
          script: |
            cd /opt/moodplay
            git pull
            docker compose --env-file .env.production -f docker-compose.prod.yml down
            docker compose --env-file .env.production -f docker-compose.prod.yml build
            docker compose --env-file .env.production -f docker-compose.prod.yml up -d
