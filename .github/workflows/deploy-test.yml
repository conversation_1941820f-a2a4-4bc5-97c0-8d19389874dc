name: Deploy to Test Environment

on:
  push:
    branches: [ develop ]  # 当推送到 develop 分支时触发

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: SSH and Deploy
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.TEST_SERVER_HOST }}
          username: ${{ secrets.TEST_SERVER_USER }}
          key: ${{ secrets.TEST_SERVER_SSH_KEY }}
          script: |
            cd /opt/moodplay
            git pull
            docker compose --env-file .env.test -f docker-compose.test.yml down
            docker compose --env-file .env.test -f docker-compose.test.yml build
            docker compose --env-file .env.test -f docker-compose.test.yml up -d