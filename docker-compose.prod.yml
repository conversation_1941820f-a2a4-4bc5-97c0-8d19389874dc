services:
  backend:
    container_name: moodplay-backend
    image: moodplay-backend:latest
    restart: always
    ports:
      - "3001:3000"
    environment:
      - NODE_ENV=production
      - DB_USER=${DB_USER}
      - DB_PASSWORD=${DB_PASSWORD}
      - DB_HOST=mysql
      - DB_NAME=${DB_NAME}
      - DB_PORT=3306
      - WECHAT_APP_ID=${WECHAT_APP_ID}
      - WECHAT_APP_SECRET=${WECHAT_APP_SECRET}
      - JWT_SECRET=${JWT_SECRET}
      - OSS_ACCESS_KEY_ID=${OSS_ACCESS_KEY_ID}
      - OSS_ACCESS_KEY_SECRET=${OSS_ACCESS_KEY_SECRET}
      - OSS_BUCKET=${OSS_BUCKET}
      - OSS_PUBLIC_BUCKET=${OSS_PUBLIC_BUCKET}
      - OSS_ENDPOINT=${OSS_ENDPOINT}
      - USE_CDN=${USE_CDN}
      - CDN_DOMAIN=${CDN_DOMAIN}
      - AUTO_REFRESH_CDN=${AUTO_REFRESH_CDN}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - AI_BASE_URL=${AI_BASE_URL}
    depends_on:
      - mysql
      - redis
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    networks:
      - moodplay-network

  admin-frontend:
    container_name: moodplay-admin-frontend
    image: moodplay-admin-frontend:latest
    ports:
      - "5173:80"
    restart: always
    networks:
      - moodplay-network

  mysql:
    container_name: moodplay-mysql
    image: mysql:8-oraclelinux8
    restart: always
    environment:
      - MYSQL_ROOT_PASSWORD=${DB_PASSWORD}
      - MYSQL_DATABASE=${DB_NAME}
      - MYSQL_USER=${DB_USER}
      - MYSQL_PASSWORD=${DB_PASSWORD}
    volumes:
      - mysql_prod_data:/var/lib/mysql
    ports:
      - "3306:3306"
    command: --default-authentication-plugin=mysql_native_password --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci --default-time-zone='+08:00'
    networks:
      - moodplay-network

  redis:
    container_name: redis:7-alpine
    image: redis:alpine
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - redis_prod_data:/data
    # 优化 Redis 配置，基于你的偏好
    command: redis-server --appendonly no --save "" --maxmemory-policy allkeys-lru
    networks:
      - moodplay-network

networks:
  moodplay-network:
    driver: bridge
    name: moodplay-prod-network

volumes:
  mysql_prod_data:
  redis_prod_data: