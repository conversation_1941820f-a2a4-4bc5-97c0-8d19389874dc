#!/bin/bash

# 生产服务器镜像加载和启动脚本
set -e

echo "🚀 开始加载镜像并启动服务..."

# 检查必要文件是否存在
if [ ! -f "moodplay-backend.tar" ]; then
    echo "❌ 错误: moodplay-backend.tar 文件不存在"
    exit 1
fi

if [ ! -f "moodplay-admin-frontend.tar" ]; then
    echo "❌ 错误: moodplay-admin-frontend.tar 文件不存在"
    exit 1
fi

if [ ! -f "docker-compose.prod.yml" ]; then
    echo "❌ 错误: docker-compose.prod.yml 文件不存在"
    exit 1
fi

if [ ! -f ".env" ]; then
    echo "❌ 错误: .env 文件不存在，请从 .env.production.server 复制并配置"
    exit 1
fi

# 1. 加载镜像
echo "📦 加载 backend 镜像..."
docker load -i moodplay-backend.tar

echo "📦 加载 admin-frontend 镜像..."
docker load -i moodplay-admin-frontend.tar

# 2. 停止旧服务（如果存在）
echo "🛑 停止旧服务..."
docker-compose -f docker-compose.prod.yml down || true

# 3. 启动新服务
echo "🚀 启动新服务..."
docker-compose -f docker-compose.prod.yml up -d

# 4. 检查服务状态
echo "🔍 检查服务状态..."
sleep 5
docker-compose -f docker-compose.prod.yml ps

echo "✅ 部署完成！"
echo ""
echo "📋 服务访问信息："
echo "- Backend API: http://localhost:3000"
echo "- Admin Frontend: http://localhost:5174"
echo "- MySQL: localhost:3307"
echo "- Redis: localhost:6380"
echo ""
echo "🔧 常用命令："
echo "# 查看日志"
echo "docker-compose -f docker-compose.prod.yml logs -f"
echo ""
echo "# 重启服务"
echo "docker-compose -f docker-compose.prod.yml restart"
echo ""
echo "# 停止服务"
echo "docker-compose -f docker-compose.prod.yml down"
