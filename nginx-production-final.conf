# /etc/nginx/conf.d/default.conf

# --- Disable Server Header ---
server_tokens off;

# --- SSL Settings (Session Cache, Stapling etc) ---
ssl_session_cache shared:SSL:10m;
ssl_session_timeout 1d;
ssl_session_tickets off;

    proxy_buffering off;          # 关闭代理缓冲，直接流式传输
    proxy_request_buffering off;  # 关闭请求缓冲（适用于大文件上传）

    # 仍然保留一定的缓冲区，但调小（用于元数据）
    proxy_buffer_size 4k;
    proxy_buffers 8 4k;

    # 超时优化（大文件需要更长传输时间）
    proxy_connect_timeout 300s;
    proxy_send_timeout 300s;
    send_timeout 300s;

    # 禁用 gzip（base64 压缩收益低，且耗 CPU）
    gzip off;

    # 启用 HTTP/2 多路复用
    http2_body_preread_size 3m;   # 提升 HTTP/2 大文件传输效率
    http2_max_concurrent_streams 32;

# key 鉴权
map $http_x_codejoy_api $api_access {
        default 0; # 默认值为0，代表未授权
        include /etc/nginx/keys.conf;
}

# FastAPI 后端服务
upstream ai_proxy_backend {
    server fastapi:8000;
    least_conn; # 使用最少连接算法
    keepalive 32; # 保持连接池
}

# PostgREST API 服务
upstream postgrest_backend {
    server postgrest:3000;
    least_conn;
    keepalive 16;
}

# === MOODPLAY 项目配置 ===
# Moodplay Backend API 服务
upstream moodplay_backend {
    server 127.0.0.1:3001;  # 对应 docker-compose 中的端口映射
    least_conn;
    keepalive 16;
}

# Moodplay Admin Frontend 服务
upstream moodplay_admin {
    server 127.0.0.1:5173;  # 对应 docker-compose 中的端口映射
    least_conn;
    keepalive 8;
}

# --- Server block for handling HTTPS requests on port 8003 ---
server {
    listen 8090 ssl ;
    listen [::]:8090 ssl;
    http2 on;
    # IMPORTANT: Replace with your actual domain or IP
    server_name wechat.codejoyai.com;

    # SSL Certificate Configuration
    ssl_certificate /opt/cert/codejoyai.com.pem; # Adjust filename if needed
    ssl_certificate_key /opt/cert/codejoyai.com.key; # Adjust filename if needed

    # Security Best Practices
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers off;
    ssl_ciphers ECDH+AESGCM:ECDH+CHACHA20:DH+AESGCM:ECDH+AES256:DH+AES256:ECDH+AES128:DH+AES:RSA+AESGCM:RSA+AES:!aNULL:!MD5:!DSS;

    # Security Headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Permissions-Policy "interest-cohort=()" always;

    # --- Proxy Settings ---
    location / {
        if ($api_access = 0) {
            # 返回 401 Unauthorized 状态码
            # 也可以返回 403 Forbidden，但401更符合"需要凭证"的语义
            return 401;
        }

        proxy_pass http://ai_proxy_backend;
        # Standard Proxy Headers
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Connection "";
        chunked_transfer_encoding off;  # 禁用分块编码（某些客户端可能有问题）

        # 超时覆盖
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $server_name;
        proxy_set_header X-Forwarded-Port $server_port;
        # Timeouts
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
    }

    # --- PostgREST API 路由 ---
    location /api/ {
        #limit_req zone=api_limit burst=20 nodelay;
        #add_header X-Debug-Api-Key-Raw "$http_x_codejoy_api" always;
        #add_header X-Debug-Api-Access-Status "$api_access" always;
        if ($api_access = 0) {
            # 返回 401 Unauthorized 状态码
            # 也可以返回 403 Forbidden，但401更符合"需要凭证"的语义
            return 401;
        }
        # 重写URL，移除 /api 前缀
        #
        rewrite ^/api/(.*)$ /$1 break;

        proxy_pass http://postgrest_backend;

        # 标准代理头
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $server_name;
        proxy_set_header X-Forwarded-Port $server_port;

        # PostgREST 特定头
        proxy_set_header Accept-Profile $arg_profile;
        proxy_set_header Content-Profile $arg_profile;

        # 超时设置
        proxy_read_timeout 60s;
        proxy_connect_timeout 10s;
        proxy_send_timeout 60s;

        # 错误处理
        proxy_next_upstream error timeout http_500 http_502 http_503 http_504;
    }

}

# === MOODPLAY 项目 SERVER 配置 ===
# --- Server block for moodplay.top domain ---
server {
    listen 8090 ssl;
    listen [::]:8090 ssl;
    http2 on;
    server_name moodplay.top *.moodplay.top;

    # SSL Certificate Configuration (使用相同的证书或配置新的)
    ssl_certificate /opt/cert/codejoyai.com.pem; # 需要替换为 moodplay.top 的证书
    ssl_certificate_key /opt/cert/codejoyai.com.key; # 需要替换为 moodplay.top 的证书

    # Security Best Practices
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers off;
    ssl_ciphers ECDH+AESGCM:ECDH+CHACHA20:DH+AESGCM:ECDH+AES256:DH+AES256:ECDH+AES128:DH+AES:RSA+AESGCM:RSA+AES:!aNULL:!MD5:!DSS;

    # Security Headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Permissions-Policy "interest-cohort=()" always;

    # === MOODPLAY ADMIN FRONTEND ===
    # 管理后台 - 访问路径: https://moodplay.top/admin/
    location /admin/ {
        proxy_pass http://moodplay_admin/admin/;

        # 标准代理头
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $server_name;
        proxy_set_header X-Forwarded-Port $server_port;

        # 前端静态资源缓存
        location ~* /admin/.*\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            proxy_pass http://moodplay_admin;
            expires 1y;
            add_header Cache-Control "public, immutable";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # 超时设置
        proxy_read_timeout 60s;
        proxy_connect_timeout 10s;
        proxy_send_timeout 60s;
    }

    # === MOODPLAY BACKEND API ===
    # API 路由 - 访问路径: https://moodplay.top/api/
    location /api/ {
        # 重写URL，移除 /api 前缀
        rewrite ^/api/(.*)$ /$1 break;

        proxy_pass http://moodplay_backend;

        # 标准代理头
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $server_name;
        proxy_set_header X-Forwarded-Port $server_port;

        # API 超时设置
        proxy_read_timeout 120s; # 后端响应读取超时2分钟
        proxy_connect_timeout 60s; # 连接后端超时1分钟
        proxy_send_timeout 60s; # 发送请求到后端超时1分钟

        # 错误处理
        proxy_next_upstream error timeout http_500 http_502 http_503 http_504;
    }

    # === 默认路由处理 ===
    # 如果没有匹配的路径，返回 404 或重定向到管理后台
    location / {
        # 可以选择重定向到管理后台
        return 301 https://$server_name/admin/;

        # 或者返回自定义的 404 页面
        # return 404;
    }
}
