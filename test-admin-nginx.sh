#!/bin/bash

# 测试 admin-frontend nginx 配置
echo "🧪 测试 admin-frontend nginx 配置..."

# 构建测试镜像
echo "📦 构建测试镜像..."
docker build -t test-admin-frontend --target production ./admin-frontend

# 启动测试容器
echo "🚀 启动测试容器..."
docker run -d --name test-admin-nginx -p 8080:80 test-admin-frontend

# 等待容器启动
sleep 3

echo "🔍 测试访问路径..."

# 测试根路径
echo "测试 http://localhost:8080/"
curl -s -o /dev/null -w "Status: %{http_code}\n" http://localhost:8080/

# 测试 /admin/ 路径
echo "测试 http://localhost:8080/admin/"
curl -s -o /dev/null -w "Status: %{http_code}\n" http://localhost:8080/admin/

# 测试静态资源
echo "测试静态资源路径..."
curl -s -o /dev/null -w "Status: %{http_code}\n" http://localhost:8080/admin/assets/ || echo "静态资源路径可能不存在（正常）"

# 清理
echo "🧹 清理测试环境..."
docker stop test-admin-nginx
docker rm test-admin-nginx
docker rmi test-admin-frontend

echo "✅ 测试完成！"
echo ""
echo "📋 如果看到 Status: 200，说明配置正确"
echo "📋 接下来需要在客户的 Nginx 中配置反向代理到 localhost:5173"
